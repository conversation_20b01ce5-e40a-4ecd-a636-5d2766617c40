/**
 * Utility functions for user operations
 */

const { User } = require('../../models/user.model'); 
const authUtils = require('../../auth/utils/authUtils');
const { getSessionToken, generateUUID } = require('../../token/accessTokenUtils');
const { ENDPOINTS } = require('../../config/config'); 
const { createOrUpdateSession } = require('../../token/userTokenUtils');
const { getHeader, makeApiCall,verifyUser } = require('../../utils/abhaUtils');  
/**
 * Find a user by any login identifier (mobile, ABHA number, ABHA address, email)
 * @param {Object} identifiers Object containing possible identifiers
 * @param {string} identifiers.mobile Mobile number
 * @param {string} identifiers.abhaNumber ABHA number
 * @param {string} identifiers.abhaAddress ABHA address
 * @param {string} identifiers.email Email address
 * @param {string} identifiers.loginIdentifier Generic identifier (will be analyzed to determine type)
 * @returns {Promise<Object>} User object or null if not found
 */
const findUserByAnyIdentifier = async (identifiers) => {
  const { mobile, abhaNumber, abhaAddress, email, _id, loginIdentifier } = identifiers;

  // At least one identifier is required
  if (!mobile && !abhaNumber && !abhaAddress && !email && !loginIdentifier && !_id) {
    return null;
  }
  if(_id){
    return await User.findById(_id);
  }

  // Build query based on provided identifiers
  const query = {};

  if (mobile) {
    query.mobile = mobile;
  }

  if (abhaNumber) {
    query["$or"] = query["$or"] || [];
    query["$or"].push({ "profiles.abhaNumber": abhaNumber });
    query["$or"].push({ "abhaAddresses.abhaNumber": abhaNumber });
  }

  if (abhaAddress) {
    query["$or"] = query["$or"] || [];
    query["$or"].push({ "profiles.abhaAddress": abhaAddress });
    query["$or"].push({ "profiles.abhaId": abhaAddress }); // For backward compatibility
    query["$or"].push({ "abhaAddresses.abhaAddress": abhaAddress });
  }

  if (email) {
    query["$or"] = query["$or"] || [];
    query["$or"].push({ email: email });
  }

  if (loginIdentifier) {
    // Try to determine the type of identifier and add appropriate query
    if (loginIdentifier.includes('@')) {
      if (loginIdentifier.includes('@abdm') || loginIdentifier.includes('@sbx')) {
        // It's an ABHA address
        query["$or"] = query["$or"] || [];
        query["$or"].push({ "profiles.abhaAddress": loginIdentifier });
        query["$or"].push({ "profiles.abhaId": loginIdentifier });
        query["$or"].push({ "abhaAddresses.abhaAddress": loginIdentifier });
      } else {
        // It's an email
        query["$or"] = query["$or"] || [];
        query["$or"].push({ email: loginIdentifier });
      }
    } else if (loginIdentifier.match(/^\d{2}-\d{4}-\d{4}-\d{4}$/)) {
      // It's an ABHA number
      query["$or"] = query["$or"] || [];
      query["$or"].push({ "profiles.abhaNumber": loginIdentifier });
      query["$or"].push({ "abhaAddresses.abhaNumber": loginIdentifier });
    } else {
      // Assume it's a mobile number
      query["$or"] = query["$or"] || [];
      query["$or"].push({ mobile: loginIdentifier });
    }
  }

  // Find user with any of the provided identifiers
  return await User.findOne(query);
};

/**
 * Create a new user with mobile number
 * @param {string} mobile Mobile number
 * @returns {Promise<Object>} Created user object
 */
const createUserWithMobile = async (mobile) => {
  console.log(`Created new user with mobile ${mobile}`);
  return await User.create({
    mobile,
    profiles: [],
    abhaAddresses: []
  });
};
const createUserWithAbhaAddress = async (abhaAddress) => {
  console.log(`Created new user with abhaAddress ${abhaAddress}`);
  return await User.create({
    preferredAbhaAddress: abhaAddress,
    abhaAddresses: [{ abhaAddress, isMain: true }],
    profiles: [{
      _Id: generateUUID(),
      abhaAddress
    }] // No profiles yet

  });
};

const createUserWithAbhaNumber = async (abhaNumber) => {
  console.log(`Created new user with abhaNumber ${abhaNumber}`);
  return await User.create({
    abhaAddresses: [{ abhaNumber, isMain: true }],
    profiles: [{
      _Id: generateUUID(),
      abhaNumber,
    }] // No profiles yet

  });
};

const createUserWithEmail = async (email) => {
  console.log(`Created new user with email ${email}`);
  return await User.create({
    email,
    profiles: [{
      _Id: generateUUID()
    }]
  });
};

/**
 * Format the first ABHA account from ABDM API response
 * @param {Array} accounts Array of ABHA accounts from ABDM API response
 * @returns {Object|null} Formatted ABHA account or null if no accounts
 */
const formatAbhaAccounts = (accounts) => {
  if (!accounts || !Array.isArray(accounts) || accounts.length === 0) {
    return null;
  }

  // Only process the first account
  const account = accounts[0];
  return {
    abhaNumber: account.ABHANumber,
    preferredAbhaAddress: account.preferredAbhaAddress,
    name: account.name,
    gender: account.gender,
    dob: account.dob,
    status: account.status,
    kycVerified: account.kycVerified
  };
};


/**
 * Update user profile with full profile details from ABDM API
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated user object
 */
const updateUserWithFullProfileDetails = async (userId) => {
  try {
    if (!userId) {
      console.log('Invalid input for updateUserWithFullProfileDetails');
      return null;
    }

    console.log(`Fetching full profile details for user ${userId} using ABHA token`);

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      console.error(`User not found with ID: ${userId}`);
      return null;
    }

    // Import profileUtils to get full profile details
    const profileUtils = require('./profileUtils');

    // Call ABDM API to get full profile details
    const profileDetails = await profileUtils.getFullProfileDetails(userId);

    if (!profileDetails) {
      console.error('Failed to fetch profile details from ABDM API');
      return null;
    }


    // Update main user level fields directly
    if (profileDetails.email) user.email = profileDetails.email;
    if (profileDetails.emailVerified) user.emailVerified = profileDetails.emailVerified;
    if (profileDetails.mobile) user.mobile = profileDetails.mobile;
    if (profileDetails.mobileVerified) user.mobileVerified = profileDetails.mobileVerified;
    if (profileDetails.abhaLinkedCount) user.abhaLinkedCount = profileDetails.abhaLinkedCount;

    // Update address at user level
    user.address = user.address || {};
    if (profileDetails.address) user.address.line = profileDetails.address;
    if (profileDetails.districtName) user.address.districtName = profileDetails.districtName;
    if (profileDetails.stateName) user.address.stateName = profileDetails.stateName;
    if (profileDetails.pinCode) user.address.pinCode = profileDetails.pinCode;
    if (profileDetails.districtCode) user.address.districtCode = profileDetails.districtCode;
    if (profileDetails.stateCode) user.address.stateCode = profileDetails.stateCode;
    if (profileDetails.townName) user.address.townName = profileDetails.townName;
    // This is where is set preferredAbhaAddress
    user.preferredAbhaAddress = profileDetails.abhaAddress;
    await user.save();

    // Process the profile details using the same approach as processAbhaAccounts
    // Convert the profile details to an account format that can be processed
    const account = {
      abhaNumber: profileDetails.abhaNumber,
      abhaAddress: profileDetails.abhaAddress,
      firstName: profileDetails.firstName,
      lastName: profileDetails.lastName,
      middleName: profileDetails.middleName,
      name: profileDetails.name || profileDetails.fullName,
      fullName: profileDetails.fullName,
      gender: profileDetails.gender,
      dayOfBirth: profileDetails.dayOfBirth,
      monthOfBirth: profileDetails.monthOfBirth,
      yearOfBirth: profileDetails.yearOfBirth,
      dateOfBirth: profileDetails.dateOfBirth,
      dob: profileDetails.dateOfBirth || profileDetails.dayOfBirth,
      email: profileDetails.email,
      mobile: profileDetails.mobile,
      profilePhoto: profileDetails.profilePhoto,
      status: profileDetails.status,
      emailVerified: profileDetails.emailVerified,
      mobileVerified: profileDetails.mobileVerified,
      kycStatus: profileDetails.kycStatus,
      abhaLinkedCount: profileDetails.abhaLinkedCount,
      authMethods: profileDetails.authMethods,
      townName: profileDetails.townName,
      profilePhotoFile: profileDetails.profilePhotoFile,
      // preferredAbhaAddress: profileDetails.preferredAbhaAddress,
      address: {
        line: profileDetails.address || "",
        districtName: profileDetails.districtName || "",
        stateName: profileDetails.stateName || "",
        pinCode: profileDetails.pinCode || "",
        districtCode: profileDetails.districtCode || "",
        stateCode: profileDetails.stateCode || "",
        townName: profileDetails.townName || ""
      }
    };

    // Use the existing processAbhaAccounts function to process this account
    // We pass a single account as an array and maintain the user's current switchProfileEnabled setting
    await processAbhaAccounts(userId, [account], user.switchProfileEnabled);

    // console.log(`Successfully updated user ${userId} with full profile details`);
    // try { await profileUtils.generateABHACard(userId, profileDetails.abhaAddress); }
    // catch (error) { console.error(`Error generating ABHA card for ${profileDetails.abhaAddress}:`, error); }
    // Return the updated user
    return await User.findById(userId).lean();
  } catch (error) {
    console.error('Error updating user with full profile details:', error);
    // Don't throw the error, just return null to prevent login failures
    return null;
  }
};


const updateMobileNumber = async (userId) => {
  try {
    const sessionToken = await getSessionToken(userId);
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Import getUserSessionToken from userTokenUtils
    const userTokenUtils = require('../../token/userTokenUtils');

    const X_TOKEN = await userTokenUtils.getUserSessionToken(userId, user.preferredAbhaAddress);
    // Use encryptionUtils for encryption
    const encryptionUtils = require('../../utils/encryptionUtils');
    const encrptedMobileNumber = await encryptionUtils.encryptMobile(user.mobile, sessionToken);
     
    const headers = {
      ...getHeader(sessionToken),
      'X-token': X_TOKEN,
    }
    const requestBody = {
      scope: [
        "abha-enrol",
        "mobile-verify"
      ],
      loginHint: "mobile",
      loginId: encrptedMobileNumber,
      otpSystem: "abdm"
    }; 
    const response = await makeApiCall(ENDPOINTS.MOBILE_GENERATE_OTP, requestBody, sessionToken, { headers });
    // {
    //   "txnId": "1234567890:20211216223812",
    //   "message": "OTP sent to mobile number ending with ******2418"
    //  }
    return response.data;
  } catch (error) {
    console.error('Error updating mobile number:', error);
    throw new Error('Failed to update mobile number');
  }
}

const verifyMobileOTP = async (userId, otp, txnId) => {
  try {
    const sessionToken = await getSessionToken(userId);
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Import getUserSessionToken from userTokenUtils
    const userTokenUtils = require('../../token/userTokenUtils');

    const X_TOKEN = await userTokenUtils.getUserSessionToken(userId, user.preferredAbhaAddress);
    // Use encryptionUtils for encryption
    const encryptionUtils = require('../../utils/encryptionUtils');
    const encryptedOTP = await encryptionUtils.encryptOTP(otp, sessionToken) 
    const headers = {
      ...getHeader(sessionToken),
      'X-token': X_TOKEN,
    }
    const requestBody = {
      scope: [
        "abha-profile",
        "mobile-verify"
      ],
      authData: {
        authMethods: [
          "otp"
        ],
        otp: {
          txnId: txnId,
          otpValue: encryptedOTP
        }
      }
    }; 
    const response = await makeApiCall(ENDPOINTS.MOBILE_VERIFY_OTP, requestBody, sessionToken, { headers });
    // {
    //   "txnId": "01bb3a4c-4588-4734-aff4-23d3978e50be",
    //   "authResult": "success",
    //   "message": "Mobile Number linked successfully",
    //   "accounts": [
    //   {
    //   "ABHANumber": "91-4173-3253-XXXX"
    //   }
    //   ]
    //  }

    return response.data;
  } catch (error) {
    console.error('Error updating mobile number:', error);
    throw new Error('Failed to update mobile number');
  }
}


/**
 * Process ABHA accounts from authentication response and update user profiles
 * @param {string} userId - User ID
 * @param {Array} accounts - Array of ABHA accounts from authentication response
 * @param {boolean} switchProfileEnabled - Whether profile switching is enabled
 * @returns {Promise<Object>} - Updated user object
 */
const processAbhaAccounts = async (userId, accounts, switchProfileEnabled = true) => {
  try {
    // Validate input parameters
    if (!userId || !accounts || !Array.isArray(accounts) || accounts.length === 0) {
      console.log('Invalid input for processAbhaAccounts');
      return null;
    }

    console.log(`Processing ${accounts.length} ABHA accounts for user ${userId}`);

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      console.error(`User not found with ID: ${userId}`);
      return null;
    }

    // Update user's switchProfileEnabled flag
    user.switchProfileEnabled = switchProfileEnabled;
    console.log(`Setting switchProfileEnabled to ${switchProfileEnabled} for user ${userId}`);

    // Check if we need to update the user's mobile number at the user level
    // We only update the user-level mobile if it's not already set
    if (!user.mobile) {
      // Look for a mobile number in any of the accounts
      for (const account of accounts) {
        if (account.mobile) {
          user.mobile = account.mobile;
          console.log(`Updated user's mobile number to ${account.mobile}`);
          break;
        }
      }
    }

    // Process each account
    for (const account of accounts) {
      try {
        // Extract all fields from the account first
        const accountFields = extractAccountFields(account);

        // Log the account being processed
        console.log(`Processing ABHA account: ${accountFields.abhaNumber} (${accountFields.abhaAddress})`);

        // Process ABHA address in user.abhaAddresses array
        processAbhaAddress(user, accountFields, accountFields.abhaAddress);

        // Process profile in user.profiles array
        processProfile(user, accountFields, accountFields.abhaAddress);

      } catch (accountError) {
        console.error(`Error processing ABHA account:`, accountError);
        // Continue with next account
      }
    }



    // If user has no preferred ABHA address but has valid ABHA addresses, set the first one as preferred
    if (!user.preferredAbhaAddress && user.abhaAddresses && user.abhaAddresses.length > 0) {
      const firstValidAddress = user.abhaAddresses.find(addr => addr.abhaAddress);
      if (firstValidAddress && firstValidAddress.abhaAddress) {
        user.preferredAbhaAddress = firstValidAddress.abhaAddress;
        console.log(`Set user's preferredAbhaAddress to ${firstValidAddress.abhaAddress} as previous one was removed`);
      }
    }

    // Save the updated user
    await user.save();
    console.log(`User ${userId} updated with ${user.profiles.length} profiles from ${accounts.length} ABHA accounts`);

    return user;
  } catch (error) {
    console.error('Error processing ABHA accounts:', error);
    // Don't throw the error, just return null to prevent login failures
    return null;
  }
};

/**
 * Extract all fields from an ABHA account
 * @param {Object} account - ABHA account object
 * @returns {Object} - Extracted fields
 */
function extractAccountFields(account) {
  // Extract core identifiers
  const abhaNumber = account.abhaNumber || account.ABHANumber;
  const abhaAddress = account.abhaAddress || account.preferredAbhaAddress;

  // Extract name information
  const fullName = account.name || account.fullName || '';
  let firstName = account.firstName || '';
  let lastName = account.lastName || '';
  let middleName = account.middleName || '';

  // Parse name if not already provided
  if (!firstName && fullName) {
    const nameParts = fullName.trim().split(' ');
    if (nameParts.length === 1) {
      firstName = nameParts[0];
    } else if (nameParts.length === 2) {
      firstName = nameParts[0];
      lastName = nameParts[1];
    } else if (nameParts.length > 2) {
      firstName = nameParts[0];
      middleName = nameParts.slice(1, -1).join(' ');
      lastName = nameParts[nameParts.length - 1];
    }
  }

  // Extract date of birth information
  let dob = null;
  let age = null;
  let dayOfBirth = account.dayOfBirth || '';
  let monthOfBirth = account.monthOfBirth || '';
  let yearOfBirth = account.yearOfBirth || '';

  // Parse DOB if available
  if (account.dob) {
    try {
      dob = new Date(account.dob);
      if (!isNaN(dob.getTime())) {
        // Calculate age
        const today = new Date();
        age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
          age--;
        }

        // Extract day, month, year
        dayOfBirth = dob.getDate().toString();
        monthOfBirth = (dob.getMonth() + 1).toString();
        yearOfBirth = dob.getFullYear().toString();
      }
    } catch (dobError) {
      console.error(`Error parsing DOB: ${account.dob}`, dobError);
    }
  } else if (yearOfBirth) {
    // If only year is available
    const year = parseInt(yearOfBirth);
    if (!isNaN(year)) {
      const month = monthOfBirth ? parseInt(monthOfBirth) - 1 : 0;
      const day = dayOfBirth ? parseInt(dayOfBirth) : 1;
      dob = new Date(year, month, day);

      // Calculate age
      const today = new Date();
      age = today.getFullYear() - year;
      if (month > today.getMonth() || (month === today.getMonth() && day > today.getDate())) {
        age--;
      }
    }
  }

  // Extract other profile information
  const gender = account.gender || '';
  const email = account.email || '';
  // Mobile is stored at user level, not in profiles
  const profilePhoto = account.profilePhoto || null;
  const kycStatus = account.kycVerified || account.kycStatus || false;
  const status = account.status || 'ACTIVE';

  // Extract address information
  const address = account.address || {};

  return {
    abhaNumber,
    abhaAddress,
    firstName: firstName || 'User', // Default name if not available
    lastName: lastName || '',
    middleName,
    fullName,
    dob: dob || new Date(2000, 0, 1), // Default to Jan 1, 2000
    age: age || 24, // Default age
    dayOfBirth,
    monthOfBirth,
    yearOfBirth,
    gender: gender || 'O', // Default gender. Valid value can be M, F, O, D, U, XXX and T'
    email,
    // Note: mobile field should not be included in profile objects as it's stored at user level
    profilePhoto,
    kycStatus,
    status,
    address: {
      line: address.line || account.address || '',
      districtName: address.districtName || account.districtName || '',
      stateName: address.stateName || account.stateName || '',
      pinCode: address.pinCode || account.pinCode || '',
      districtCode: address.districtCode || account.districtCode || '',
      stateCode: address.stateCode || account.stateCode || '',
      townName: address.townName || account.townName || ''
    }
  };
}

/**
 * Process ABHA address for a user
 * @param {Object} user - User object
 * @param {Object} fields - Extracted fields from account
 */
function processAbhaAddress(user, fields, abhaAddress) {

  // Check if this ABHA address already exists in user's abhaAddresses
  const existingAbhaAddressIndex = user.abhaAddresses.findIndex(addr => addr.abhaAddress === abhaAddress && abhaAddress);

  if (existingAbhaAddressIndex === -1) {
    // Add to abhaAddresses array if not exists
    user.abhaAddresses.push({
      abhaAddress: abhaAddress,
      abhaNumber: fields.abhaNumber,
      isMain: user.abhaAddresses.length === 0, // Make main if first address
      createdAt: new Date()
    });

    console.log(`Added new ABHA address to user: ${abhaAddress || fields.abhaNumber}`);

  } else {
    // Update existing ABHA address
    const existingAddress = user.abhaAddresses[existingAbhaAddressIndex];

    // Remove abhaNumber if it doesn't exist in the account being processed
    if (existingAddress.abhaNumber && !fields.abhaNumber) {
      console.log(`Removing abhaNumber ${existingAddress.abhaNumber} from abhaAddress as it's not in the response`);
      existingAddress.abhaNumber = null;
    }

    // Remove abhaAddress if it doesn't exist in the account being processed
    if (existingAddress.abhaAddress && !abhaAddress) {
      console.log(`Removing abhaAddress ${existingAddress.abhaAddress} from abhaAddresses as it's not in the response`);
      existingAddress.abhaAddress = null;

    }

    // Update fields if they were missing in the existing record but present in the response
    if (!existingAddress.abhaNumber && fields.abhaNumber) {
      existingAddress.abhaNumber = fields.abhaNumber;
      console.log(`Added abhaNumber ${fields.abhaNumber} to existing abhaAddress`);
    }

    if (!existingAddress.abhaAddress && abhaAddress) {
      existingAddress.abhaAddress = abhaAddress;
      console.log(`Added abhaAddress ${abhaAddress} to existing record`);
    }

    console.log(`Updated existing ABHA address: ${abhaAddress || fields.abhaNumber}`);
  }

  // Clean up abhaAddresses array - remove entries with null abhaAddress
  const validAddresses = user.abhaAddresses.filter(addr => addr.abhaAddress);
  if (validAddresses.length < user.abhaAddresses.length) {
    console.log(`Removed ${user.abhaAddresses.length - validAddresses.length} invalid abhaAddresses entries`);
    user.abhaAddresses = validAddresses;
  }
}

/**
 * Process profile for a user
 * @param {Object} user - User object
 * @param {Object} fields - Extracted fields from account
 */
function processProfile(user, fields, abhaAddress) {

  // Check if a profile with this ABHA address or number already exists
  const profileWithAbhaIndex = user.profiles.findIndex((p => p.abhaAddress === abhaAddress && abhaAddress));

  // Check if this is the main profile
  const isMainProfile = profileWithAbhaIndex >= 0 ?
    user.profiles[profileWithAbhaIndex].isMain :
    user.profiles.length === 0;

  // Update main user level fields if this is the main profile or if these fields are not set yet
  if (isMainProfile || !user.email) {
    if (fields.email) user.email = fields.email;
    if (fields.emailVerified) user.emailVerified = fields.emailVerified;
  }

  if (isMainProfile || !user.mobile) {
    // Only update mobile at user level if it's not already set
    if (!user.mobile && fields.mobile) {
      user.mobile = fields.mobile;
    }
    if (fields.mobileVerified) user.mobileVerified = fields.mobileVerified;
  }

  if (isMainProfile || !user.abhaLinkedCount) {
    if (fields.abhaLinkedCount) user.abhaLinkedCount = fields.abhaLinkedCount;
  }

  // Update address at user level if this is the main profile or if address is not set yet
  if (isMainProfile || !user.address || !user.address.line) {
    user.address = user.address || {};

    // Update address fields if available
    if (fields.townName) user.address.townName = fields.townName;

    // Update address if available
    if (fields.address) {
      if (fields.address.line) user.address.line = fields.address.line;
      if (fields.address.districtName) user.address.districtName = fields.address.districtName;
      if (fields.address.stateName) user.address.stateName = fields.address.stateName;
      if (fields.address.pinCode) user.address.pinCode = fields.address.pinCode;
      if (fields.address.districtCode) user.address.districtCode = fields.address.districtCode;
      if (fields.address.stateCode) user.address.stateCode = fields.address.stateCode;
      if (fields.address.townName) user.address.townName = fields.address.townName;
    }
  }

  if (profileWithAbhaIndex >= 0) {
    // Update existing profile
    const profile = user.profiles[profileWithAbhaIndex];

    // Remove abhaNumber if it doesn't exist in the account being processed
    if (profile.abhaNumber && !fields.abhaNumber) {
      console.log(`Removing abhaNumber ${profile.abhaNumber} from profile as it's not in the response`);
      profile.abhaNumber = null;
    }

    // Remove abhaAddress if it doesn't exist in the account being processed
    if (profile.abhaAddress && !abhaAddress) {
      user.profiles.splice(profileWithAbhaIndex, 1);
      console.log(`Removed profile with abhaAddress ${profile.abhaAddress} from user's profiles`);
      return; // Exit the function as the profile has been removed
    }

    // Update core identifiers if they exist in the response
    if (fields.abhaNumber) profile.abhaNumber = fields.abhaNumber;
    if (abhaAddress) profile.abhaAddress = abhaAddress;

    // Update name if available
    if (fields.firstName) profile.firstName = fields.firstName;
    if (fields.lastName) profile.lastName = fields.lastName;
    if (fields.middleName) profile.middleName = fields.middleName;
    if (fields.fullName) profile.fullName = fields.fullName;

    // Update gender if available
    if (fields.gender) profile.gender = fields.gender;

    // Update DOB and age if available
    if (fields.dob && !isNaN(fields.dob.getTime())) {
      profile.dob = fields.dob;
      profile.age = fields.age;
      if (fields.dayOfBirth) profile.dayOfBirth = fields.dayOfBirth;
      if (fields.monthOfBirth) profile.monthOfBirth = fields.monthOfBirth;
      if (fields.yearOfBirth) profile.yearOfBirth = fields.yearOfBirth;
      if (fields.dateOfBirth) profile.dateOfBirth = fields.dateOfBirth;
    }

    // These fields are now only at the main user level

    // Update profile photo if available
    if (fields.profilePhoto) profile.profilePhoto = fields.profilePhoto;
    if (fields.profilePhotoFile) profile.profilePhotoFile = fields.profilePhotoFile;

    // Update KYC status if available
    if (fields.kycStatus) profile.kycStatus = fields.kycStatus;

    // Update status if available
    if (fields.status) profile.status = fields.status;

    // Update authentication methods if available
    if (fields.authMethods && Array.isArray(fields.authMethods)) {
      profile.authMethods = fields.authMethods;
    }

    // These fields are now only at the main user level

    console.log(`Updated existing profile for ABHA address: ${abhaAddress || fields.abhaNumber}`);
  } else {
    // Create new profile
    try {
      const newProfile = {
        _Id: generateUUID(),
        abhaNumber: fields.abhaNumber,
        abhaAddress: abhaAddress,
        firstName: fields.firstName,
        lastName: fields.lastName,
        middleName: fields.middleName,
        fullName: fields.fullName,
        gender: fields.gender,
        dob: fields.dob,
        age: fields.age,
        dayOfBirth: fields.dayOfBirth,
        monthOfBirth: fields.monthOfBirth,
        yearOfBirth: fields.yearOfBirth,
        dateOfBirth: fields.dateOfBirth,

        profilePhoto: fields.profilePhoto,
        profilePhotoFile: fields.profilePhotoFile,
        kycStatus: fields.kycStatus,
        status: fields.status,
        authMethods: fields.authMethods,
        isMain: user.profiles.length === 0, // Set as main if it's the first profile
        createdAt: new Date()
      };

      user.profiles.push(newProfile);
      console.log(`Created new profile for ABHA address: ${abhaAddress || fields.abhaNumber}`);
    } catch (profileError) {
      console.error(`Error creating profile for ABHA address: ${abhaAddress || fields.abhaNumber}`, profileError);
    }
  }
}

// Format date as "DD Month YYYY"
const formatDateString = (dateObj) => {
  if (!dateObj || !(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
    return null;
  }

  const day = dateObj.getDate().toString().padStart(2, '0');
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  const month = months[dateObj.getMonth()];
  const year = dateObj.getFullYear();

  return `${day} ${month} ${year}`;
};

const updateUserAndCreateUserSession = async (accounts, user, txnId, tokenData, req = {}) => {


  // Process all accounts and update/create profiles for each using the utility function
  try {
    if(!tokenData){
      throw new Error('Token data is required');
    }
    // Generate local tokens for our app
    const accessToken = authUtils.generateToken(user, 'access');
    const refreshToken = authUtils.generateToken(user, 'refresh');

    await createOrUpdateSession({
      userId: user._id,
      refreshToken,
      accessToken,
      ipAddress: req?.ip || '0.0.0.0',
      userAgent: req?.headers?.['user-agent'] || 'Unknown',
    });

    // Log the number of accounts being processed
    console.log(`Processing ${accounts.length} ABHA accounts for user ${user._id}`);

    // Process ABHA accounts first - this should create/update all profiles
    const switchProfileEnabled = tokenData.switchProfileEnabled || true;
    console.log(`Switch profile enabled: ${switchProfileEnabled}`);

    // Find and update user with switchProfileEnabled and ensure we get all profiles
    await processAbhaAccounts(user._id, accounts, switchProfileEnabled);

    // Refresh user data after processing accounts
    let updatedUser = await User.findById(user._id);

    console.log(`User has ${updatedUser.profiles.length} profiles`)

    // Use the temporaly preferred ABHA address from the updated user for now
    const mainProfile = updatedUser.profiles.find(p=> p.isMain);
    const abhaAddress = mainProfile?.abhaAddress;
    if (abhaAddress && mainProfile) {
       try {
         
         await verifyUser(updatedUser._id, abhaAddress, txnId, tokenData.token);

         // Update user with full profile details for the preferred address
         updatedUser = await updateUserWithFullProfileDetails(updatedUser._id);

         console.log(`User ${updatedUser._id} updated with preferred ABHA address: ${updatedUser.preferredAbhaAddress} and ABHA accounts information`);
       } catch (verifyError) {
         console.error(`Failed to verify user with abhaAddress ${abhaAddress}:`, verifyError.message);
         // Don't throw error, just log it and continue
         console.log(`Continuing without verification for user ${updatedUser._id}`);
       }
    } else {
      console.log(`No main profile or ABHA address found for user ${updatedUser._id}, skipping verification`);
    }
    updatedUser = await User.findById(user._id).lean();
    console.log(`User ${updatedUser._id} updated with ABHA card`);
    console.log(`User has ${updatedUser.profiles.length} profiles`)


    return {
      user: updatedUser,
      accessToken
    };
  } catch (processError) {
    console.error('Error processing ABHA accounts:', processError);
    throw processError;
  }
}

const updateABHANumber = async (userId, abhaAddress, abhaNumber) => {
  // Update current profile with abhaNumber
  const user = await User.findById(userId);
  if (!user) {
    throw new Error("User not found");
  }
  const currentProfile = user.profiles.find(p => p.abhaAddress === abhaAddress);
  if (!currentProfile) {
    throw new Error(`Profile with ID: ${profileId} not found`);
  }
  if (currentProfile) {
    currentProfile.abhaNumber = abhaNumber;
  }
  // Update abhaAddresses with abhaNumber
  const abhaAddressIndex = user.abhaAddresses.findIndex(addr => addr.abhaAddress === abhaAddress);
  if (abhaAddressIndex !== -1) {
    user.abhaAddresses[abhaAddressIndex].abhaNumber = abhaNumber;
  }

  await user.save();
}

module.exports = {
  findUserByAnyIdentifier,
  createUserWithMobile,
  createUserWithAbhaAddress,
  createUserWithAbhaNumber,
  createUserWithEmail,
  formatAbhaAccounts,
  updateUserWithFullProfileDetails,
  processAbhaAccounts,
  formatDateString,
  updateUserAndCreateUserSession,
  updateMobileNumber,
  verifyMobileOTP,
  updateABHANumber
};
